<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsUser extends Model
{
    use HasUlids;

    protected $table = 'acs_users';

    protected $fillable = [
        'name',
        'email',
        'username',
        'phone',
        'password',
        'acs_role_id',
        'acs_coorperative_id',
        'acs_coorperative_branch_id',
        'invited_by',
        'status',
        'activated_at',
    ];

    // Relationships
    public function role()
    {
        return $this->belongsTo(AcsRole::class, 'acs_role_id');
    }

    public function commissionChanges()
    {
        return $this->hasMany(AcsCommissionChange::class, 'action_by');
    }

    public function sales()
    {
        return $this->hasMany(AcsSales::class, 'acs_users_id');
    }
}
